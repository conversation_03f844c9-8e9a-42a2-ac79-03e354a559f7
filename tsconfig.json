{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@src/*": ["src/*"], "@auth/*": ["src/auth/*"], "@health/*": ["src/health/*"], "@websocket/*": ["src/websocket/*"], "@common/*": ["src/common/*"], "@base/*": ["src/common/base/*"], "@configs/*": ["src/common/configs/*"], "@database/*": ["src/common/database/*"], "@decorators/*": ["src/common/decorators/*"], "@filters/*": ["src/common/filters/*"], "@guards/*": ["src/common/guards/*"], "@i18n/*": ["src/common/i18n/*"], "@middlewares/*": ["src/common/middlewares/*"], "@permissions/*": ["src/common/permissions/*"], "@utils/*": ["src/common/utils/*"], "@modules/*": ["src/modules/*"]}}}