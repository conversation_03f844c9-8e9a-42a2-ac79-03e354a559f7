{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "assets": [{"include": "/common/i18n/**/*", "watchAssets": true, "outDir": "dist"}, {"include": "/modules/mail/assets/**/*", "watchAssets": true, "outDir": "dist"}, {"include": "/modules/mail/mail-templates/**/*", "watchAssets": true, "outDir": "dist"}, {"include": "/modules/notifications/templates/**/*", "watchAssets": true, "outDir": "dist"}], "plugins": [{"name": "@nestjs/swagger", "options": {"introspectComments": true}}]}}