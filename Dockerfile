# Stage 1: Dependencies
FROM node:20.18.0-alpine AS deps
WORKDIR /app

# Install dependencies required for node-gyp
RUN apk add --no-cache python3 make g++

# Create non-root user early
RUN addgroup -g 1001 -S appgroup && adduser -u 1001 -S appuser -G appgroup

# Copy package files
COPY package.json yarn.lock ./

# First install only production dependencies - better caching
RUN --mount=type=cache,target=/root/.yarn/cache \
    yarn install --frozen-lockfile --production --prefer-offline && \
    cp -R node_modules prod_modules && \
    # Then install all dependencies for build phase
    yarn install --frozen-lockfile --prefer-offline && \
    yarn cache clean

# Stage 2: Builder
FROM node:20.18.0-alpine AS builder
WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Build with production optimizations
ENV NODE_ENV=production
RUN yarn build \
    && rm -rf node_modules \
    && yarn cache clean

# Stage 3: Runtime
FROM node:20.18.0-alpine AS runtime
WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache tini fontconfig ttf-dejavu freetype harfbuzz nss tzdata

# Copy built application and production dependencies
COPY --from=builder /app/dist ./dist
COPY --from=deps /app/prod_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=deps /etc/passwd /etc/passwd
COPY --from=deps /etc/group /etc/group

# Set permissions and build font cache
RUN chown -R appuser:appgroup /app  && \
    chown -R appuser:appgroup /home/<USER>
    fc-cache -f

# Switch to non-root user
USER appuser

# Use tini as entrypoint
ENTRYPOINT ["/sbin/tini", "--"]

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/v1/health || exit 1

# Start the application
CMD ["node", "dist/main"]
