import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Logger } from '@nestjs/common';
import { I18nValidationExceptionFilter, I18nValidationPipe } from 'nestjs-i18n';
import { initializeTransactionalContext } from 'typeorm-transactional';
import { ValidationPipe, BadRequestException } from '@nestjs/common';
import { HttpFilterException } from './common/filters/http-exception.filter';
import { ICreateUserWebsiteDto } from '@src/modules/users/dto/create-user-website.dto';
import * as cors from 'cors';

async function bootstrap() {
  initializeTransactionalContext();

  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(
    new ValidationPipe({
      // whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      //enable in next phase
      exceptionFactory: (errors) => {
        const isCreateUserDto = errors.some(
          (err) => err.target instanceof ICreateUserWebsiteDto,
        );
        if (isCreateUserDto) {
          const formattedErrors: Record<string, string[]> = {};

          errors.forEach((error) => {
            const field = error.property;
            const constraints = Object.values(error.constraints || {});

            const codes = constraints.map((msg) => {
              const parts = msg.split('.');
              return parts.length > 1 ? parts[1] : msg;
            });

            if (formattedErrors[field]) {
              formattedErrors[field].push(...codes);
            } else {
              formattedErrors[field] = codes;
            }
          });
          return new BadRequestException({ message: formattedErrors });
        }
        const defaultMessages = errors.flatMap((err) =>
          Object.values(err.constraints || {}),
        );
        return new BadRequestException({ message: defaultMessages });
      },
    }),
  );

  app.use(cors({ exposedHeaders: ['Content-Disposition'] }));

  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type,Authorization',
    // credentials: true
  });

  // Swagger Setting
  const config = new DocumentBuilder()
    .setTitle('SIT group assignment API')
    .setDescription('API documentation for SIT group assignment application')
    .setVersion('1.0')
    .addBearerAuth() // Add Bearer Auth for JWT
    .addServer('/api')
    .build();

  app.useGlobalPipes(
    new I18nValidationPipe({ whitelist: true, transform: true }),
  );

  app.useGlobalFilters(
    new HttpFilterException(),
    new I18nValidationExceptionFilter({
      detailedErrors: true,
    }),
  );

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/v1/docs', app, document);
  // const document = SwaggerModule.createDocument(app, config);
  // SwaggerModule.setup('api', app, document);

  app.useLogger(new Logger());
  app.setGlobalPrefix('/api');

  await app.listen(3000);
  Logger.log('Application is running on: http://localhost:3000');
}

bootstrap();
