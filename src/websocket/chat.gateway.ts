import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketServer,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { createClient } from 'redis';
import { createAdapter } from '@socket.io/redis-adapter';
import { UseGuards, Logger } from '@nestjs/common';
import { JwtAuthGuard } from '@src/common/guards/jwt-auth.guard';
import { ChatroomService } from '../modules/chat/services/chatroom.service';
import { MessageService } from '../modules/chat/services/message.service';
import { CreateMessageDto } from '../modules/chat/dto/create-message.dto';
import { JoinChatroomDto } from '../modules/chat/dto/join-chatroom.dto';

@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true,
  },
  path: '/api/socket.io',
})
@UseGuards(JwtAuthGuard)
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ChatGateway.name);

  constructor(
    private readonly chatroomService: ChatroomService,
    private readonly messageService: MessageService,
  ) {}

  async onModuleInit() {
    const pubClient = createClient({
      url: `${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || 6379}`,
      password: process.env.REDIS_PASSWORD || undefined,
    });
    const subClient = pubClient.duplicate();

    await pubClient.connect();
    await subClient.connect();

    this.server.adapter(createAdapter(pubClient, subClient));
    console.log('Redis adapter initialized');
    console.log('[Server]: Socket.io Server started with Redis Pub/Sub!');
  }

  handleConnection(client: Socket) {
    this.logger.log(`Socket connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Socket disconnected: ${client.id}`);
    // Leave all rooms when client disconnects
    const rooms = Array.from(client.rooms);
    rooms.forEach((room) => {
      if (room !== client.id) {
        client.leave(room);
        this.logger.log(`Client ${client.id} left room ${room}`);
      }
    });
  }

  @SubscribeMessage('joinRoom')
  async handleJoinRoom(
    @MessageBody() data: JoinChatroomDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const userId =
        client.handshake.auth?.userId || client.handshake.query?.userId;

      if (!userId) {
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

      // Verify user can join this chatroom
      const chatroom = await this.chatroomService.findOne(
        data.chatroomId,
        userId,
      );

      // Join the socket room
      await client.join(data.chatroomId);

      this.logger.log(`Client ${client.id} joined room ${data.chatroomId}`);

      // Notify other users in the room
      client.to(data.chatroomId).emit('userJoined', {
        userId,
        chatroomId: data.chatroomId,
        timestamp: new Date(),
      });

      // Send confirmation to the client
      client.emit('joinedRoom', {
        chatroomId: data.chatroomId,
        chatroom,
        timestamp: new Date(),
      });

      // Send recent messages to the newly joined client
      const recentMessages = await this.messageService.getRecentMessages(
        data.chatroomId,
        userId,
        20,
      );

      client.emit('recentMessages', {
        chatroomId: data.chatroomId,
        messages: recentMessages.reverse(),
      });
    } catch (error) {
      this.logger.error(`Error joining room: ${error.message}`);
      client.emit('error', {
        message: 'Failed to join room',
        details: error.message,
      });
    }
  }

  @SubscribeMessage('leaveRoom')
  async handleLeaveRoom(
    @MessageBody() data: { chatroomId: string },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const userId =
        client.handshake.auth?.userId || client.handshake.query?.userId;

      if (!userId) {
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

      // Leave the socket room
      await client.leave(data.chatroomId);

      this.logger.log(`Client ${client.id} left room ${data.chatroomId}`);

      // Notify other users in the room
      client.to(data.chatroomId).emit('userLeft', {
        userId,
        chatroomId: data.chatroomId,
        timestamp: new Date(),
      });

      // Send confirmation to the client
      client.emit('leftRoom', {
        chatroomId: data.chatroomId,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Error leaving room: ${error.message}`);
      client.emit('error', {
        message: 'Failed to leave room',
        details: error.message,
      });
    }
  }

  @SubscribeMessage('sendMessage')
  async handleSendMessage(
    @MessageBody() data: CreateMessageDto,
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const userId =
        client.handshake.auth?.userId || client.handshake.query?.userId;

      if (!userId) {
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

      // Create the message in the database
      const message = await this.messageService.create(data, userId);

      // Emit the message to all users in the chatroom
      this.server.to(data.chatroomId).emit('newMessage', {
        message,
        timestamp: new Date(),
      });

      this.logger.log(
        `Message sent by ${userId} in room ${data.chatroomId}: ${message.id}`,
      );
    } catch (error) {
      this.logger.error(`Error sending message: ${error.message}`);
      client.emit('error', {
        message: 'Failed to send message',
        details: error.message,
      });
    }
  }

  @SubscribeMessage('typing')
  handleTyping(
    @MessageBody() data: { chatroomId: string; isTyping: boolean },
    @ConnectedSocket() client: Socket,
  ) {
    const userId =
      client.handshake.auth?.userId || client.handshake.query?.userId;

    if (!userId) {
      return;
    }

    // Broadcast typing status to other users in the room
    client.to(data.chatroomId).emit('userTyping', {
      userId,
      chatroomId: data.chatroomId,
      isTyping: data.isTyping,
      timestamp: new Date(),
    });
  }
}
