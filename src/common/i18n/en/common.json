{"access_denied": "Access denied!", "internal_server": "Internal server", "validation": {"isEmail": "{property} must be a valid email address", "isDate": "{property} must be a valid date", "length": "{property} must be between $constraint1 and $constraint2 characters long", "isNotEmpty": "{property} should not be empty", "minLength": "{property} must be at least $constraint1 characters", "maxLength": "{property} must be at most $constraint1 characters", "isPassword": "{property} must contain at least one letter, one number and be at least 8 characters", "phoneInvalid": "{property} must be a valid phone number", "invalidCountryCode": "{property} must be in the format +X, +XX, +XXX, etc."}}