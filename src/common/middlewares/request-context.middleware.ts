import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RequestContextMiddleware implements NestMiddleware {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  use(req: Request, res: Response, next: NextFunction) {
    const requestId = uuidv4();
    req['requestId'] = requestId;

    // Extract userId from JWT token
    try {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const decoded = this.jwtService.verify(token, {
          secret: this.configService.get('AUTH_JWT_SECRET'),
        });
        if (decoded && decoded.userId) {
          req['userId'] = decoded.email;
        }
      }
    } catch (error) {
      // Continue even if token is invalid or expired
      // We don't want to block the request, just not set userId
    }

    next();
  }
}

// Factory function to create middleware with dependencies
export function requestContextMiddlewareFactory(
  jwtService: JwtService,
  configService: ConfigService,
): (req: Request, res: Response, next: NextFunction) => void {
  const middleware = new RequestContextMiddleware(jwtService, configService);
  return middleware.use.bind(middleware);
}
