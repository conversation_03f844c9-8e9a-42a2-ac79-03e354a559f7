// import { registerAs } from '@nestjs/config';
// import { AWSConfig } from './config.type';

// export default registerAs<AWSConfig>('aws', () => {
//   console.log("process.env.ACCESS_KEY_ID", process.env.ACCESS_KEY_ID)
//   return {
//     accessKeyId: process.env.ACCESS_KEY_ID ?? '',
//     secretAccessKey: process.env.SECRET_ACCESS_KEY ?? '',
//     region: process.env.AWS_S3_REGION ?? '',
//     bucket: process.env.AWS_DEFAULT_S3_BUCKET ?? '',
//   };
// });
