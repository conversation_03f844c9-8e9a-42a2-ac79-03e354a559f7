import { registerAs } from '@nestjs/config';
import { NotificationConfig } from './config.type';
import validateConfig from '../utils/validate-config';
import { IsOptional, IsString, IsUrl } from 'class-validator';

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  NOTIFICATION_ADMIN_EMAILS: string;

  @IsString()
  @IsOptional()
  NOTIFICATION_ADMIN_PHONES: string;

  @IsUrl()
  @IsOptional()
  NOTIFICATION_LARK_WEBHOOK_URL: string;

  @IsString()
  @IsOptional()
  NOTIFICATION_LARK_SECURE_KEYWORD: string;
}

export default registerAs<NotificationConfig>('notification', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  const adminEmails = process.env.NOTIFICATION_ADMIN_EMAILS
    ? process.env.NOTIFICATION_ADMIN_EMAILS.split(',')
    : [];

  const adminPhones = process.env.NOTIFICATION_ADMIN_PHONES
    ? process.env.NOTIFICATION_ADMIN_PHONES.split(',')
    : [];

  return {
    adminEmails,
    adminPhones,
    larkWebhookUrl: process.env.NOTIFICATION_LARK_WEBHOOK_URL,
    larkSecureKeyword: process.env.NOTIFICATION_LARK_SECURE_KEYWORD || 'AIPCON',
  };
});
