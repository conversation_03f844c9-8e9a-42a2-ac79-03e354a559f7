import { registerAs } from '@nestjs/config';
import { MongodbConfig } from './config.type';
import {
  IsOptional,
  IsInt,
  Min,
  Max,
  IsString,
  ValidateIf,
  IsBoolean,
} from 'class-validator';
import validateConfig from '../utils/validate-config';

class EnvironmentVariablesValidator {
  @ValidateIf((envValues) => envValues.MONGODB_URI)
  @IsString()
  MONGODB_URI: string;

  @ValidateIf((envValues) => !envValues.MONGODB_URI)
  @IsString()
  MONGODB_HOST: string;

  @ValidateIf((envValues) => !envValues.MONGODB_URI)
  @IsInt()
  @Min(0)
  @Max(65535)
  @IsOptional()
  MONGODB_PORT: number;

  @ValidateIf((envValues) => !envValues.MONGODB_URI)
  @IsString()
  @IsOptional()
  MONGODB_PASSWORD: string;

  @ValidateIf((envValues) => !envValues.MONGODB_URI)
  @IsString()
  MONGODB_DATABASE: string;

  @ValidateIf((envValues) => !envValues.MONGODB_URI)
  @IsString()
  @IsOptional()
  MONGODB_USERNAME: string;

  @IsInt()
  @IsOptional()
  MONGODB_MAX_CONNECTIONS: number;

  @IsBoolean()
  @IsOptional()
  MONGODB_SSL_ENABLED: boolean;

  @IsString()
  @IsOptional()
  MONGODB_AUTH_SOURCE: string;

  @IsString()
  @IsOptional()
  MONGODB_REPLICA_SET: string;
}

export default registerAs<MongodbConfig>('mongodb', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    uri: process.env.MONGODB_URI,
    host: process.env.MONGODB_HOST || 'localhost',
    port: process.env.MONGODB_PORT
      ? parseInt(process.env.MONGODB_PORT, 10)
      : 27017,
    password: process.env.MONGODB_PASSWORD,
    database: process.env.MONGODB_DATABASE || 'sit_chat',
    username: process.env.MONGODB_USERNAME,
    maxConnections: process.env.MONGODB_MAX_CONNECTIONS
      ? parseInt(process.env.MONGODB_MAX_CONNECTIONS, 10)
      : 100,
    sslEnabled: process.env.MONGODB_SSL_ENABLED === 'true',
    authSource: process.env.MONGODB_AUTH_SOURCE || 'admin',
    replicaSet: process.env.MONGODB_REPLICA_SET,
  };
});
