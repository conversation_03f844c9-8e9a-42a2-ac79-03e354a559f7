import { Injectable, LoggerService, Scope, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { REQUEST } from '@nestjs/core';
import * as winston from 'winston';
import 'winston-daily-rotate-file';
import * as fs from 'fs';
import * as path from 'path';
import { trace } from 'console';

@Injectable()
export class CustomLogger implements LoggerService {
  private logger: winston.Logger;

  constructor(
    @Inject(REQUEST) private readonly request: Request,
    private readonly configService: ConfigService,
  ) {
    const logTransports: winston.transport[] = [];
    if (this.configService.get<string>('LOG_CONSOLE_ENABLED') === 'true') {
      logTransports.push(new winston.transports.Console());
    }

    if (this.configService.get<string>('LOG_FILE_ENABLED') === 'true') {
      const logDir =
        this.configService.get<string>('LOG_DIR') ||
        path.join(__dirname, '../logs');

      // Ensure log directory exists
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }

      logTransports.push(
        new winston.transports.DailyRotateFile({
          dirname: logDir,
          filename:
            this.configService.get<string>('LOG_FILE_NAME_PATTERN') ||
            'application-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          zippedArchive: true,
          maxSize: this.configService.get<string>('LOG_FILE_MAX_SIZE') || '20m',
          maxFiles:
            this.configService.get<string>('LOG_FILE_RETENTION') || '14d',
        }),
      );
    }

    // Configure winston logger
    this.logger = winston.createLogger({
      level: this.configService.get<string>('LOG_LEVEL') || 'info',
      format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS ZZ' }),
        winston.format.printf(
          ({ timestamp, level, message, trace, context, ...meta }) => {
            const requestId = this.request['requestId'] || '-';
            const user = this.request['userId'] ?? 'anonymous';
            const metaString = Object.keys(meta).length
              ? ` [${JSON.stringify(meta)}]`
              : '';
            return `${timestamp} [${level.toUpperCase()}]\t[${requestId}] ${context ? `[${context}]` : ''} [${user}] ${metaString} ${message} ${trace ? `\nStack trace: ${trace}` : ''}`;
          },
        ),
      ),
      transports: logTransports,
    });
  }

  log(message: string, meta?: any) {
    this.logger.info(message, meta);
  }

  warn(message: string, meta?: any) {
    this.logger.warn(message, meta);
  }

  error(message: string, meta?: any, trace?: string) {
    if (trace) {
      meta = { ...meta, trace };
    }
    this.logger.error(message, meta);
  }

  debug(message: string, meta?: any) {
    this.logger.debug(message, meta);
  }
}
