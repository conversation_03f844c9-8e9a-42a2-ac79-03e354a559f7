import * as dayjs from 'dayjs';
import { ObjectLiteral, SelectQueryBuilder } from 'typeorm';
import { snakeCase } from 'lodash';

export const applyQueryOptions = <T extends ObjectLiteral>(
  queryBuilder: SelectQueryBuilder<T>,
  filters: any,
  searchField: string[] = [],
  searchValue?: string,
  sortField?: string,
  page: number = 1,
  limit: number = 50,
  multiSearchValue: boolean = false,
): SelectQueryBuilder<T> => {
  Object.keys(filters)
    .filter((key) => key.startsWith('f_'))
    .forEach((key, index) => {
      const field = key.replace('f_', '');
      const value: string = filters[key] as string;
      let operator = '=';
      let newValue: string | number | null | string[] = value.trim();
      const from = field.includes('.from');
      const to = field.includes('.to');

      if (from) {
        queryBuilder.andWhere(
          `${field.replace('.from', '')} >= :from_${index}`,
          {
            [`from_${index}`]: dayjs(value)
              .startOf('day')
              .format('YYYY-MM-DDTHH:mm:ssZ'),
          },
        );
        return;
      }
      if (to) {
        queryBuilder.andWhere(`${field.replace('.to', '')} <= :to_${index}`, {
          [`to_${index}`]: dayjs(value)
            .endOf('day')
            .format('YYYY-MM-DDTHH:mm:ssZ'),
        });
        return;
      }

      if (value.startsWith('!')) {
        operator = '!=';
        newValue = value.substring(1);
      } else if (value.startsWith('>=')) {
        ((operator = '>='), (newValue = value.substring(2)));
      } else if (value.startsWith('>')) {
        ((operator = '>'), (newValue = value.substring(1)));
      } else if (value.startsWith('<=')) {
        ((operator = '<='), (newValue = value.substring(2)));
      } else if (value.startsWith('<')) {
        ((operator = '<'), (newValue = value.substring(1)));
      } else if (value.startsWith('%')) {
        operator = 'ILIKE';
        newValue = `%${value.substring(1)}%`;
      } else if (value === 'null') {
        operator = 'IS NULL';
        newValue = null;
      } else if (value === '!null') {
        operator = 'IS NOT NULL';
        newValue = null;
      } else if (value.includes(',')) {
        operator = value.startsWith('!') ? 'NOT IN' : 'IN';
        newValue = value.split(',').map((v) => v.trim());
      }

      if (field.includes('.')) {
        let relation: string, column: string;
        if (field.includes('->>')) {
          const [left, lang] = field.split('->>');
          [relation, column] = left.split('.');
          column = `${snakeCase(column)}->>${lang.trim()}`;
        } else {
          [relation, column] = field.split('.');
          column = snakeCase(column);
        }

        let columnSearch: string;
        if (operator === 'ILIKE') {
          if (column.includes('->>')) {
            const [baseCol, lang] = column.split('->>');
            columnSearch = `unaccent("${relation}"."${snakeCase(baseCol)}"->>${lang.trim()})`;
          } else {
            columnSearch = `unaccent("${relation}"."${column}"::text)`;
          }
        } else {
          if (column.includes('->>')) {
            const [baseCol, lang] = column.split('->>');
            columnSearch = `"${relation}"."${snakeCase(baseCol)}"->>${lang.trim()}`;
          } else {
            columnSearch = `"${relation}".${column}`;
          }
        }

        const valueSearch =
          operator === 'ILIKE'
            ? `unaccent(:newValue_${index}::text)`
            : `:newValue_${index}`;

        if (operator === 'IS NULL' || operator === 'IS NOT NULL') {
          queryBuilder.andWhere(`${columnSearch} ${operator}`);
        } else if (Array.isArray(newValue)) {
          queryBuilder.andWhere(`${columnSearch} ${operator} (:...newValue)`, {
            newValue,
          });
        } else {
          queryBuilder.andWhere(`${columnSearch} ${operator} ${valueSearch}`, {
            [`newValue_${index}`]: newValue,
          });
        }
      } else {
        const columnSearch =
          operator === 'ILIKE'
            ? `unaccent(${snakeCase(field)}::text)`
            : snakeCase(field);

        const valueSearch =
          operator === 'ILIKE'
            ? `unaccent(:newValue_${index}::text)`
            : `:newValue_${index}`;

        if (operator === 'IS NULL' || operator === 'IS NOT NULL') {
          queryBuilder.andWhere(`${columnSearch} ${operator}`);
        } else if (Array.isArray(newValue)) {
          queryBuilder.andWhere(`${columnSearch} ${operator} (:...newValue)`, {
            newValue,
          });
        } else {
          queryBuilder.andWhere(`${columnSearch} ${operator} ${valueSearch}`, {
            [`newValue_${index}`]: newValue,
          });
        }
      }
    });

  searchValue = (searchValue ?? '').trim();

  if (searchValue && searchField.length > 0) {
    const terms = multiSearchValue
      ? searchValue.split(',').map((t) => t.trim())
      : [searchValue];
    const conditions = terms
      .map((term, i) => {
        const group = searchField
          .map((field) => {
            if (field.includes('->>')) {
              const [left, lang] = field.split('->>');
              const [relation, column] = left.split('.');
              return `unaccent("${relation}"."${snakeCase(column)}"->>${lang.trim()}) ILIKE unaccent(:term${i})`;
            } else if (field.includes('.')) {
              const [relation, column] = field.split('.');
              return `unaccent("${relation}"."${snakeCase(column)}"::text) ILIKE unaccent(:term${i})`;
            }
            return `unaccent(${snakeCase(field)}::text) ILIKE unaccent(:term${i})`;
          })
          .join(' OR ');
        return `(${group})`;
      })
      .join(' AND ');

    const paramMap = terms.reduce((acc, t, i) => {
      acc[`term${i}`] = `%${t}%`;
      return acc;
    }, {});

    queryBuilder.andWhere(`(${conditions})`, paramMap);
  }

  if (sortField) {
    const sortFields = Array.isArray(sortField)
      ? sortField
      : sortField.split(',').map((s) => s.trim());

    sortFields.forEach((field) => {
      const direction = field.startsWith('-') ? 'DESC' : 'ASC';
      const rawField = field.replace(/^-/, '');
      const dbField = rawField.includes('.') ? rawField : snakeCase(rawField);
      queryBuilder.addOrderBy(dbField, direction);
    });
  }

  const offset = (page - 1) * limit;
  queryBuilder.skip(offset).take(limit);

  return queryBuilder;
};

export const addSortMultiFields = (queryBuilder, sortField) => {
  if (sortField) {
    const sortFields = Array.isArray(sortField)
      ? sortField
      : sortField.split(',').map((s) => s.trim());

    sortFields.forEach((field) => {
      const direction = field.startsWith('-') ? 'DESC' : 'ASC';
      const rawField = field.replace(/^-/, '');
      const dbField = rawField.includes('.') ? rawField : snakeCase(rawField);
      queryBuilder.addOrderBy(dbField, direction);
    });
  }
};
