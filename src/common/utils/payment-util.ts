import * as CryptoJS from 'crypto-js';
import * as https from 'https';

export const sortObj = (obj: any) => {
  return Object.keys(obj)
    .sort()
    .reduce(function (result, key) {
      result[key] = obj[key];
      return result;
    }, {});
};

export const generateStringToHash = (paramSorted: any) => {
  let stringToHash = '';
  for (const key in paramSorted) {
    const value = paramSorted[key];
    const pref4 = key.substring(0, 4);
    const pref5 = key.substring(0, 5);
    if (pref4 == 'vpc_' || pref5 == 'user_') {
      if (key != 'vpc_SecureHash' && key != 'vpc_SecureHashType') {
        if (value.length > 0) {
          if (stringToHash.length > 0) {
            stringToHash = stringToHash + '&';
          }
          stringToHash = stringToHash + key + '=' + value;
        }
      }
    }
  }
  return stringToHash;
};

export const genSecureHash = (stringToHash, merHashCode) => {
  const merHashHex = CryptoJS.enc.Hex.parse(merHashCode);
  const keyHash = CryptoJS.HmacSHA256(stringToHash, merHashHex);
  const keyHashHex = CryptoJS.enc.Hex.stringify(keyHash).toUpperCase();
  return keyHashHex;
};

export const sendHttpsGet = (url: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    https
      .get(url, (response) => {
        const rawHeader = response.rawHeaders;
        const locationValue = extractValueByKey('location', rawHeader);
        resolve(locationValue);
      })
      .on('error', (error) => {
        console.error(`Error: ${error.message}`);
        reject(error);
      });
  });
};

export const sendHttpsPost = (url, params): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: process.env.ONEPAY_BASE_URL,
      port: 443,
      path: url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': params.toString().length,
      },
    };

    // Tạo request
    const req = https.request(options, (res) => {
      console.log(`statusCode: ${res.statusCode}`);

      res.on('data', (d) => {
        process.stdout.write(d);
        resolve(d);
      });
    });

    // Xử lý lỗi
    req.on('error', (error) => {
      console.error(error);
      reject(error);
    });

    // Gửi dữ liệu
    req.write(params.toString());
    req.end();
  });
};

function extractValueByKey(key, array) {
  for (let i = 0; i < array.length; i += 2) {
    if (array[i] === key) {
      return array[i + 1];
    }
  }
  return null;
}
