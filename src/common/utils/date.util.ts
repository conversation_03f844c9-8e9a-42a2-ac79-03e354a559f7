import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
dayjs.extend(utc);
dayjs.extend(timezone);

export const VN_TIME_ZONE = 'Asia/Ho_Chi_Minh';
export const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export function parseDate(dateStr: string | Date, timezone = VN_TIME_ZONE) {
  if (!dateStr || dateStr === 'Invalid Date') {
    return null;
  }

  return dayjs(dateStr).tz(timezone).toISOString();
}

export function formatDate(
  date: string | Date,
  dateFormat = DATE_FORMAT,
  timezone = VN_TIME_ZONE,
) {
  return dayjs(date).tz(timezone).format(dateFormat);
}
