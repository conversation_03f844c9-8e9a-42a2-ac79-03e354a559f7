import { parsePhoneNumberFromString } from 'libphonenumber-js';

export const formatPhoneNumber = (countryCode: string, phoneNumber: string) => {
  const cleanedPhoneNumber = phoneNumber.startsWith('0')
    ? phoneNumber.slice(1)
    : phoneNumber;
  const formatted = parsePhoneNumberFromString(
    phoneNumber.startsWith('+')
      ? phoneNumber
      : countryCode + cleanedPhoneNumber,
  );
  return formatted?.isValid() ? formatted : null;
};
