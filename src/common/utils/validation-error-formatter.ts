import { BadRequestException } from '@nestjs/common';
import { ValidationError } from 'class-validator';

export function createCustomValidationException(errors: ValidationError[]) {
  const formattedErrors: Record<string, string[]> = {};

  errors.forEach((error) => {
    const field = error.property;
    const constraints = Object.values(error.constraints || {});
    const codes = constraints.map((msg) => {
      const parts = msg.split('.');
      return parts.length > 1 ? parts[1] : msg;
    });

    if (formattedErrors[field]) {
      formattedErrors[field].push(...codes);
    } else {
      formattedErrors[field] = codes;
    }
  });

  return new BadRequestException({ message: formattedErrors });
}
