import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectsCommand,
} from '@aws-sdk/client-s3';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class UploadService {
  private s3: S3Client;
  private bucket: string;
  private envPrefix: string;
  private region: string;
  private endpoint: string;

  constructor() {
    const accessKeyId = process.env.ACCESS_KEY_ID ?? '';
    const secretAccessKey = process.env.SECRET_ACCESS_KEY ?? '';
    this.region = process.env.AWS_S3_REGION ?? '';
    this.bucket = process.env.AWS_DEFAULT_S3_BUCKET ?? '';
    this.endpoint = process.env.AWS_S3_ENDPOINT ?? ''; // Custom endpoint

    if (!accessKeyId || !secretAccessKey || !this.bucket) {
      throw new Error('AWS credentials are not configured');
    }

    if (!this.endpoint) {
      this.s3 = new S3Client({
        region: this.region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
      });
    } else {
      this.s3 = new S3Client({
        endpoint: this.endpoint,
        region: this.region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
        forcePathStyle: true, // enable if use (MinIO, Wasabi)
      });
    }

    this.envPrefix = process.env.NODE_ENV || 'development';
  }

  async uploadFile(
    file: Express.Multer.File,
    modelName: string,
  ): Promise<string> {
    const key = `${this.envPrefix}/${modelName}/${uuidv4()}${extname(file.originalname)}`;

    await this.s3.send(
      new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
      }),
    );

    if (this.endpoint) {
      return `${this.endpoint}/${this.bucket}/${key}`;
    }
    return `https://${this.bucket}.s3.${this.region}.amazonaws.com/${key}`;
  }

  async deleteFiles(fileUrls: string[]): Promise<void> {
    let urlPath = '';
    if (this.endpoint) {
      urlPath = `${this.endpoint}/${this.bucket}/`;
    } else {
      urlPath = `https://${this.bucket}.s3.${this.region}.amazonaws.com/`;
    }
    const objectsToDelete = fileUrls.map((url) => {
      // Extract the key from the URL
      const key = url.replace(urlPath, '');
      return { Key: key };
    });

    // Prepare the delete request
    const deleteParams = {
      Bucket: this.bucket,
      Delete: {
        Objects: objectsToDelete,
      },
    };

    try {
      // Send the delete request to S3
      await this.s3.send(new DeleteObjectsCommand(deleteParams));
    } catch {
      throw new Error('Failed to delete files from S3');
    }
  }
}
