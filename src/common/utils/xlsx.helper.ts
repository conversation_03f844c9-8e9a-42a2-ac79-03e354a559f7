import { ObjectLiteral } from 'typeorm';
import type { ColumnSchema, Schema, ValueType } from 'write-excel-file';
import writeXlsxFile from 'write-excel-file/node';

type BorderStyle =
  | 'hair'
  | 'dotted'
  | 'dashDotDot'
  | 'dashDot'
  | 'dashed'
  | 'thin'
  | 'mediumDashDotDot'
  | 'slantDashDot'
  | 'mediumDashDot'
  | 'mediumDashed'
  | 'medium'
  | 'double'
  | 'thick';

type FontWeight = 'bold';

type FontStyle = 'italic';

type Color = string;

interface ICellStyle {
  align?: 'left' | 'center' | 'right';
  alignVertical?: 'top' | 'center' | 'bottom';
  height?: number;
  span?: number;
  rowSpan?: number;
  wrap?: boolean;
  fontFamily?: string;
  fontSize?: number;
  fontWeight?: FontWeight;
  fontStyle?: FontStyle;
  color?: Color;
  backgroundColor?: Color;
  borderColor?: Color;
  borderStyle?: BorderStyle;
  leftBorderColor?: Color;
  leftBorderStyle?: BorderStyle;
  rightBorderColor?: Color;
  rightBorderStyle?: BorderStyle;
  topBorderColor?: Color;
  topBorderStyle?: BorderStyle;
  bottomBorderColor?: Color;
  bottomBorderStyle?: BorderStyle;
}

interface ISheet<T = any> {
  name: string;
  schema: Schema<T>;
  data?: any[];
}

type ColumnSchemaValue = (o: ObjectLiteral) => any;

export class ExcelBuilder {
  private _headerStyle: ICellStyle = {
    backgroundColor: '#8EA9DB',
    align: 'left',
  };

  private _sheets: ISheet[] = [];

  set headerStyle(style: ICellStyle) {
    this._headerStyle = style;
  }

  createSheet<T>(name: string, schema: Schema<T> = [], data: any[] = []) {
    const length = this._sheets.push({ name, schema, data });
    return length - 1;
  }

  setSheetSchema(
    index: number,
    ...columSchemas: ColumnSchema<ObjectLiteral, ValueType>[]
  ) {
    if (!this._sheets[index]) {
      throw new Error(`sheet index = ${index} not found`);
    }

    this._sheets[index].schema.push(...columSchemas);
    return this;
  }

  setSheetData(index: number, ...columSchemas: ObjectLiteral[]) {
    if (!this._sheets[index]) {
      throw new Error(`sheet index = ${index} not found`);
    }

    this._sheets[index].data?.push(...columSchemas);
    return this;
  }

  toBuffer(): Promise<Buffer> {
    const data = [] as any[][];
    const sheets = [] as string[];
    const schema = [] as Schema<any>[];

    this._sheets.forEach((sheet) => {
      data.push(sheet.data || []);
      sheets.push(sheet.name);
      schema.push(sheet.schema);
    });

    return writeXlsxFile(data, {
      sheets,
      headerStyle: this._headerStyle,
      schema,
      buffer: true,
    });
  }

  static numberColumnSchema(
    name: string,
    valueKey: string,
  ): ColumnSchema<ObjectLiteral, ValueType>;
  static numberColumnSchema(
    name: string,
    valueKey: string,
    styles: ObjectLiteral,
  ): ColumnSchema<ObjectLiteral, ValueType>;
  static numberColumnSchema(
    name: string,
    value: ColumnSchemaValue,
  ): ColumnSchema<ObjectLiteral, ValueType>;

  static numberColumnSchema(
    name: string,
    arg: string | ColumnSchemaValue,
    styles?: ObjectLiteral,
  ): ColumnSchema<ObjectLiteral, ValueType> {
    const width = name.length + 5;

    if (typeof arg === 'string') {
      return {
        column: name,
        type: Number,
        value: (d) => d[arg],
        width,
        ...styles,
        borderColor: '#000000',
        borderStyle: 'thin',
      };
    }

    return {
      column: name,
      type: Number,
      value: arg,
      ...styles,
      borderColor: '#000000',
      borderStyle: 'thin',
    };
  }

  static stringColumnSchema(
    name: string,
    valueKey: string,
  ): ColumnSchema<ObjectLiteral, ValueType>;
  static stringColumnSchema(
    name: string,
    valueKey: string,
    styles: ObjectLiteral,
  ): ColumnSchema<ObjectLiteral, ValueType>;
  static stringColumnSchema(
    name: string,
    value: ColumnSchemaValue,
  ): ColumnSchema<ObjectLiteral, ValueType>;
  static stringColumnSchema(
    name: string,
    arg: string | ColumnSchemaValue,
    styles?: ObjectLiteral,
  ): ColumnSchema<ObjectLiteral, ValueType> {
    const width = name.length + 5;

    if (typeof arg === 'string') {
      return {
        column: name,
        type: String,
        value: (d) => d[arg],
        width,
        ...styles,
        borderColor: '#000000',
        borderStyle: 'thin',
      };
    }

    return {
      column: name,
      type: String,
      value: arg,
      width,
      ...styles,
      borderColor: '#000000',
      borderStyle: 'thin',
    };
  }
}
