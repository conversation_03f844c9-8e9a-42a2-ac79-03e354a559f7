import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';

@Catch(HttpException)
export class HttpFilterException implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;

    const exceptionResponse = exception.getResponse();
    const version = '0.0.1';
    let message: string | string[] = 'An error occurred';
    let code = status;
    let data = null;
    let totalRow = 0;
    const success = false;

    if (typeof exceptionResponse === 'string') {
      message = exceptionResponse;
    } else if (typeof exceptionResponse === 'object') {
      const responseObj = exceptionResponse as any;
      message = responseObj.message || message;
      code = responseObj.code || code;
      data = responseObj.data || data;
      totalRow = responseObj.totalRow || totalRow;
    }

    response.status(status).json({
      version,
      code,
      message,
      data,
      totalRow,
      success,
    });
  }
}
