import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    if (
      request.url.includes('/admin/auth/login') ||
      request.url.includes('/auth/login') ||
      request.url.includes('/users/create')
    ) {
      return true;
    }

    return super.canActivate(context) as boolean;
  }

  handleRequest(err: any, user: any, info: any): any {
    if (err || !user) {
      throw new UnauthorizedException('Invalid or missing token');
    }
    return user;
  }
}
