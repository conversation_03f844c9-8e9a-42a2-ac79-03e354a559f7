import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  ForbiddenException,
} from '@nestjs/common';

import { Reflector } from '@nestjs/core';
import { PERMISSION_KEY } from '../decorators/permission.decorator';
import { HTTP } from '../http';
import { I18nService } from 'nestjs-i18n';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private http: HTTP,
    private readonly i18n: I18nService,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(
      PERMISSION_KEY,
      [context.getHandler(), context.getClass()],
    );

    const response = this.http.setHttpRequest(
      HttpStatus.FORBIDDEN,
      'CANNOT_ACCESS_THIS_FUNCTION',
    );

    if (!requiredRoles?.length) {
      return true;
    }
    const { user } = context.switchToHttp().getRequest();
    // check if SUPER ADMIN
    if (user.isAdmin && user.roles && user.roles.includes('*')) {
      return true;
    }

    const isPermission = requiredRoles.some((role) =>
      user.roles?.includes(role),
    );

    if (!isPermission) {
      throw new ForbiddenException(this.i18n.t('common.access_denied'));
    }
    return true;
  }
}
