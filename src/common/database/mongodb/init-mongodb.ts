import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';

@Injectable()
export class MongodbInitService implements OnModuleInit {
  private readonly logger = new Logger(MongodbInitService.name);

  constructor(@InjectConnection() private connection: Connection) {}

  async onModuleInit() {
    await this.initializeCollections();
  }

  private async initializeCollections() {
    try {
      this.logger.log('Initializing MongoDB collections and indexes...');

      // Initialize chatrooms collection
      await this.initializeChatroomsCollection();

      // Initialize messages collection
      await this.initializeMessagesCollection();

      this.logger.log(
        'MongoDB collections and indexes initialized successfully',
      );
    } catch (error) {
      this.logger.error('Failed to initialize MongoDB collections:', error);
      throw error;
    }
  }

  private async initializeChatroomsCollection() {
    const collection = this.connection.collection('chatrooms');

    // Create indexes for chatrooms collection
    const indexes = [
      { key: { participants: 1 }, name: 'participants_1' },
      { key: { createdBy: 1 }, name: 'createdBy_1' },
      { key: { createdAt: -1 }, name: 'createdAt_-1' },
      {
        key: { name: 'text', description: 'text' },
        name: 'text_search',
        weights: { name: 10, description: 5 },
      },
    ] as const;

    for (const index of indexes) {
      try {
        await collection.createIndex(index.key, {
          name: index.name,
          background: true,
          ...('weights' in index && index.weights
            ? { weights: index.weights }
            : {}),
        });
        this.logger.log(`Created index ${index.name} on chatrooms collection`);
      } catch (error) {
        if (error.code !== 85) {
          // Index already exists
          this.logger.warn(
            `Failed to create index ${index.name}:`,
            error.message,
          );
        }
      }
    }
  }

  private async initializeMessagesCollection() {
    const collection = this.connection.collection('messages');

    // Create indexes for messages collection
    const indexes = [
      {
        key: { chatroomId: 1, createdAt: -1 },
        name: 'chatroomId_1_createdAt_-1',
      },
      { key: { senderId: 1, createdAt: -1 }, name: 'senderId_1_createdAt_-1' },
      {
        key: { chatroomId: 1, messageType: 1 },
        name: 'chatroomId_1_messageType_1',
      },
      { key: { chatroomId: 1 }, name: 'chatroomId_1' },
      { key: { senderId: 1 }, name: 'senderId_1' },
      { key: { createdAt: -1 }, name: 'createdAt_-1' },
      {
        key: { content: 'text' },
        name: 'content_text',
        weights: { content: 1 },
      },
    ] as const;

    for (const index of indexes) {
      try {
        await collection.createIndex(index.key, {
          name: index.name,
          background: true,
          ...('weights' in index && index.weights
            ? { weights: index.weights }
            : {}),
        });
        this.logger.log(`Created index ${index.name} on messages collection`);
      } catch (error) {
        if (error.code !== 85) {
          // Index already exists
          this.logger.warn(
            `Failed to create index ${index.name}:`,
            error.message,
          );
        }
      }
    }
  }

  async dropCollections() {
    try {
      this.logger.log('Dropping MongoDB collections...');

      const collections = await this.connection.db!.listCollections().toArray();
      const collectionNames = collections.map((col) => col.name);

      if (collectionNames.includes('chatrooms')) {
        await this.connection.collection('chatrooms').drop();
        this.logger.log('Dropped chatrooms collection');
      }

      if (collectionNames.includes('messages')) {
        await this.connection.collection('messages').drop();
        this.logger.log('Dropped messages collection');
      }

      this.logger.log('MongoDB collections dropped successfully');
    } catch (error) {
      this.logger.error('Failed to drop MongoDB collections:', error);
      throw error;
    }
  }
}
