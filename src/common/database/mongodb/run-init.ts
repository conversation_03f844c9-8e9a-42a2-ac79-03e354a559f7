#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Module } from '@nestjs/common';
import { MongodbInitService } from './init-mongodb';
import mongodbConfig from '../../configs/mongodb.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [mongodbConfig],
      envFilePath: ['.env'],
    }),
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => {
        const mongoConfig = configService.get('mongodb');
        
        let uri: string;
        if (mongoConfig.uri) {
          uri = mongoConfig.uri;
        } else {
          const auth = mongoConfig.username && mongoConfig.password 
            ? `${mongoConfig.username}:${mongoConfig.password}@` 
            : '';
          const authSource = mongoConfig.authSource ? `?authSource=${mongoConfig.authSource}` : '';
          uri = `mongodb://${auth}${mongoConfig.host}:${mongoConfig.port}/${mongoConfig.database}${authSource}`;
        }

        return {
          uri,
          useNewUrlParser: true,
          useUnifiedTopology: true,
          maxPoolSize: mongoConfig.maxConnections,
          ssl: mongoConfig.sslEnabled,
          ...(mongoConfig.replicaSet && { replicaSet: mongoConfig.replicaSet }),
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [MongodbInitService],
})
class MongodbInitModule {}

async function bootstrap() {
  const app = await NestFactory.create(MongodbInitModule, {
    logger: ['log', 'error', 'warn'],
  });

  const initService = app.get(MongodbInitService);
  
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'init':
        console.log('Initializing MongoDB collections and indexes...');
        await initService.onModuleInit();
        console.log('MongoDB initialization completed successfully!');
        break;
        
      case 'drop':
        console.log('Dropping MongoDB collections...');
        await initService.dropCollections();
        console.log('MongoDB collections dropped successfully!');
        break;
        
      default:
        console.log('Usage: ts-node run-init.ts [init|drop]');
        console.log('  init - Initialize collections and create indexes');
        console.log('  drop - Drop all chat-related collections');
        process.exit(1);
    }
  } catch (error) {
    console.error('Operation failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

bootstrap();
