import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: `${process.env.AUTH_JWT_SECRET}`,
    });
  }

  validate(payload: any) {
    return {
      email: payload.email,
      userId: payload.userId,
      phoneNumber: payload.phoneNumber,
      isAdmin: payload.isAdmin,
      roles: payload.roles,
    };
  }
}
