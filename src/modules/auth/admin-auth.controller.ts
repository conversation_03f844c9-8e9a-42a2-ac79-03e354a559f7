// src/auth/auth.controller.ts
import { Controller, Post, Body } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthDto } from './dto/auth.dto';
import { ApiTags } from '@nestjs/swagger';
import { CustomLogger } from '@src/common/logging';

@ApiTags('admins')
@Controller('v1/admin/auth')
export class AdminAuthController {
  private readonly loggerMeta: any;
  constructor(
    private readonly authService: AuthService,
    private readonly logger: CustomLogger,
  ) {
    this.loggerMeta = { context: AdminAuthController.name };
  }

  @Post('/login')
  // @ApiOperation({ summary: 'Admin login' })
  // @ApiResponse({ status: 200, description: 'User successfully logged in.' })
  // @ApiResponse({ status: 400, description: 'Invalid credentials.' })
  // @ApiResponse({ status: 500, description: 'Internal server.' })
  async loginAdmin(@Body() admin: AuthDto) {
    this.logger.log(
      `Admin login attempt for user: ${admin.username}`,
      this.loggerMeta,
    );
    return {};
  }
}
