// src/auth/auth.service.ts
import { Injectable, HttpStatus, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { AuthDto } from './dto/auth.dto';
import { JwtPayload } from './jwt-payload.interface';
import { UsersService } from '../users/users.service';
import { CustomLogger } from '../../common/logging';
import { I18nService } from 'nestjs-i18n';

import { HTTP } from '../../common/http';
import * as bcrypt from 'bcrypt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService extends HTTP {
  private readonly loggerMeta: any;

  constructor(
    private readonly jwtService: JwtService,
    private readonly usersService: UsersService,
    private readonly logger: CustomLogger,
    private readonly i18n: I18nService,
    private readonly configService: ConfigService,
  ) {
    super();
    this.loggerMeta = { context: AuthService.name };
  }
}
