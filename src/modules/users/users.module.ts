import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { HTTP } from '@common/http';
import { CustomLogger } from '@common/logging';
import { MailService } from '@modules/mail/mail.service';
import { MailModule } from '@modules/mail/mail.module';

@Module({
  imports: [TypeOrmModule.forFeature([User]), MailModule],
  providers: [UsersService, HTTP, CustomLogger, MailService],
  controllers: [UsersController],
  exports: [UsersService, MailService],
})
export class UsersModule {}
