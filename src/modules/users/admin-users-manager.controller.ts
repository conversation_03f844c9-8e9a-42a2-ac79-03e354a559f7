import {
  Controller,
  UseGuards,
  Get,
  Post,
  Body,
  Param,
  Query,
  Patch,
  Delete,
  Put,
  Req,
} from '@nestjs/common';

import { Permissions } from '../../common/decorators/permission.decorator';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { PERMISSION } from '../../common/permissions/index';
import { CustomLogger } from '@src/common/logging';
import { UsersService } from './users.service';
import { AuthDto } from '../auth/dto/auth.dto';
import { CreateAdminUserDto } from './dto/create-manager-admin.dto';
import { Request } from 'express';
import {
  UpdateAdminDto,
  UpdatePasswordAdmin,
} from './dto/update-admin-manager.dto';
import { ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
@ApiBearerAuth()
@Controller('v1/admin/manager')
@UseGuards(PermissionGuard)
export class AdminUsersManagerController {
  private readonly loggerMeta: any;
  constructor(
    private readonly adminUserService: UsersService,
    private readonly logger: CustomLogger,
  ) {
    this.loggerMeta = { context: AdminUsersManagerController.name };
  }
  @Get()
  @Permissions(PERMISSION.ACCOUNT.ADMIN.VIEW)
  @ApiQuery({ name: 'search', required: false })
  @ApiQuery({ name: 'sort', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  findAllAdmin(
    @Query('sort') sortField?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 50,
  ) {
    return this.adminUserService.findAllAdmin(sortField, page, limit);
  }
}
