import {
  Controller,
  UseGuards,
  Get,
  Post,
  Param,
  Body,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  Request,
} from '@nestjs/common';

import { Permissions } from '../../common/decorators/permission.decorator';
import { PermissionGuard } from '../../common/guards/permission.guard';
import { PERMISSION } from '../../common/permissions/index';
import { UsersService } from './users.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { SearchPagingDto } from './dto/search-paging.dto';
import { User } from './entities/user.entity';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { ChangePasswordDto } from './dto/change-password.dto';
import { JwtAuthGuard } from '@src/common/guards/jwt-auth.guard';

@ApiBearerAuth()
@Controller('v1/admin/users')
@UseGuards(PermissionGuard)
export class AdminUsersController {
  constructor(private readonly userService: UsersService) {}

  @Get()
  @Permissions(PERMISSION.ACCOUNT.USER.VIEW)
  @ApiQuery({ name: 'search', required: false })
  @ApiQuery({ name: 'sort', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  async getUsers(
    @Query() filters: Record<string, string>,
    @Query('search') searchValue?: string,
    @Query('sort') sortField?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 50,
  ): Promise<{ data: User[]; total: number }> {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return this.userService.getAllUsers(
      filters,
      searchValue,
      sortField,
      page,
      limit,
    );
  }
}
