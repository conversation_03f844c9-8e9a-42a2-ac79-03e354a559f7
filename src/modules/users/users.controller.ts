import {
  Controller,
  Query,
  Get,
  Post,
  Body,
  Put,
  Req,
  Delete,
} from '@nestjs/common';
import { CustomLogger } from '@src/common/logging';
import { UsersService } from './users.service';
import { ApiBearerAuth, ApiTags, ApiBody } from '@nestjs/swagger';

@ApiBearerAuth()
@Controller('v1/users')
export class UsersController {
  private readonly loggerMeta: any;
  constructor(
    private readonly userService: UsersService,
    private readonly logger: CustomLogger,
  ) {
    this.loggerMeta = { context: UsersController.name };
  }

  // @Get()
  // getUsers() {
  //   return "List Users"
  // }
}
