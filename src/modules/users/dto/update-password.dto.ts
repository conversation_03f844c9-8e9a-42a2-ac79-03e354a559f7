import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  MinLength,
  Matches,
} from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';
export class UpdatePasswordDto {
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  email?: string;

  @IsNotEmpty()
  @IsString()
  @Matches(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: i18nValidationMessage('common.validation.isPassword'),
  })
  newPassword?: string;
}
