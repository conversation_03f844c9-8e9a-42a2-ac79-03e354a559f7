import {
  Is<PERSON>rray,
  IsEmail,
  <PERSON>NotEmpty,
  IsS<PERSON>,
  IsOptional,
} from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class CreateAdminUserDto {
  /**
   * The username of the user. This can be an email or phone number.
   * @example "0987654321" or "<EMAIL>"
   */
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsEmail({}, { message: i18nValidationMessage('common.validation.isEmail') })
  email: string;

  @IsNotEmpty()
  departmentId: string;

  @IsArray()
  @IsNotEmpty()
  roleIds: string[];

  @IsArray()
  @IsOptional()
  loungeIds: string[];
}
