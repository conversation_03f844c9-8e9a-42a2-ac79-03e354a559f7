import {
  IsEmail,
  IsEnum,
  isNotEmpty,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
  IsDate,
} from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';
import { Transform } from 'class-transformer';

enum Gender {
  MALE = 'Male',
  FEMALE = 'Female',
}

export class ICreateUserWebsiteDto {
  @IsString()
  @IsOptional()
  name: string;

  @IsEnum(Gender)
  @IsOptional()
  gender: Gender = Gender.MALE;

  @IsEmail({}, { message: 'email.isNotEmail' })
  @IsNotEmpty({ message: 'email.isNotEmpty' })
  email: string;

  @IsNotEmpty({ message: 'password.isNotEmpty' })
  @IsString({ message: 'password.isString' })
  @Matches(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: 'password.invalidFormat',
  })
  password: string;

  @IsNotEmpty({})
  @IsString()
  @Matches(/^\+\d{1,4}$/, { message: 'countryCode.invalid' })
  countryCode: string;

  @IsString({ message: 'phoneNumber.isString' })
  @IsNotEmpty({ message: 'phoneNumber.isNotEmpty' })
  @Matches(/\d{9,14}$/, {
    message: 'phoneNumber.invalidFormat',
  })
  phoneNumber: string;

  @IsOptional()
  @Transform(({ value }) => new Date(value))
  @IsDate({ message: 'birthday.invalidDate' })
  birthday?: string;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  nationality: string;

  @IsOptional()
  @IsString()
  affiliateCode: string;

  @IsOptional()
  @IsString()
  customerSourceOther?: string;
}

export class verifyPhoneDto {
  @IsNotEmpty({ message: 'phoneNumber.isNotEmpty' })
  @IsString({ message: 'phoneNumber.isString' })
  phoneNumber: string;
  @IsNotEmpty({ message: 'prefix.isNotEmpty' })
  @IsString({ message: 'prefix.isNotEmpty' })
  prefix: string;
}
