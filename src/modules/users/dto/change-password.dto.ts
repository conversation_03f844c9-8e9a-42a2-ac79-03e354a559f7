import { ApiProperty } from '@nestjs/swagger';
import { IsString, Matches } from 'class-validator';

export class ChangePasswordDto {
  updated_date?: Date;
  updated_by?: number;

  @IsString()
  @ApiProperty({ description: 'Old password', required: true })
  oldPassword: string;

  @IsString()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}/,
    {
      message:
        'Password must be at least 8 characters, uppercase & lowercase, at least 1 number & 1 special character',
    },
  )
  @ApiProperty({ description: 'New password', required: false })
  newPassword: string;

  @IsString()
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}/,
    {
      message:
        'Password must be at least 8 characters, uppercase & lowercase, at least 1 number & 1 special character',
    },
  )
  @ApiProperty({ description: 'Confirm password', required: false })
  confirmPassword: string;
}
