import {
  IsEmail,
  IsEnum,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Length,
  <PERSON><PERSON><PERSON>th,
  Matches,
  IsArray,
} from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class UpdateAdminDto {
  /**
   * The username of the user. This can be an email or phone number.
   * @example 2025-02-17 14:30:00+07:00
   */

  @IsNotEmpty()
  @IsString()
  name?: string;

  @IsNotEmpty()
  @IsArray()
  roleIds?: string[];

  @IsArray()
  @IsOptional()
  loungeIds: string[];

  @IsNotEmpty()
  departmentId: string;

  @IsNotEmpty()
  isActive: boolean;
}

export class UpdatePasswordAdmin {
  /**
   * The username of the user. This can be an email or phone number.
   * @example 2025-02-17 14:30:00+07:00
   */

  @IsNotEmpty()
  @IsEmail({}, { message: i18nValidationMessage('common.validation.isEmail') })
  email: string;

  @IsNotEmpty()
  @IsString()
  @Matches(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: i18nValidationMessage('common.validation.isPassword'),
  })
  newPassword: string;
}
