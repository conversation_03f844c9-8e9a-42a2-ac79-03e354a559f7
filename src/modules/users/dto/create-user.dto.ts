import {
  IsEmail,
  <PERSON>Enum,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
} from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

enum Gender {
  MALE = 'Male',
  FEMALE = 'Female',
}

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEnum(Gender)
  @IsNotEmpty()
  gender: Gender;

  @IsEmail({}, { message: i18nValidationMessage('common.validation.isEmail') })
  @IsNotEmpty()
  email: string;

  @IsOptional()
  @IsString()
  @Matches(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/, {
    message: i18nValidationMessage('common.validation.isPassword'),
  })
  password: string;

  @IsOptional()
  @IsString()
  @Matches(/^\+\d{1,4}$/, {
    message: i18nValidationMessage('common.validation.invalidCountryCode'),
  })
  countryCode: string;

  @IsString()
  @Length(9, 15)
  @IsNotEmpty()
  @Matches(/^(0|\+)\d{9,14}$/, {
    message: i18nValidationMessage('common.validation.phoneInvalid'),
  })
  phoneNumber: string;

  @IsOptional()
  @IsString()
  birthday?: string;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  affiliateCode: string;

  @IsOptional()
  @IsString()
  nationality: string;

  @IsNotEmpty()
  @IsString()
  customerSourceId: string;

  @IsOptional()
  @IsString()
  customerSourceOther?: string;
}
