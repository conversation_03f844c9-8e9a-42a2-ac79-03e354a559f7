import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  BeforeInsert,
  ManyToMany,
  JoinTable,
  JoinColumn,
  DeleteDateColumn,
  Index,
} from 'typeorm';
import * as bcrypt from 'bcrypt';
import { BaseEntity } from '@src/common/base/entities/base.entity';

@Entity('users')
export class User extends BaseEntity {
  @Index('idx_user_name')
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Index('idx_user_email', { unique: true })
  @Column({ type: 'varchar', length: 255, unique: true, nullable: true })
  email: string;

  @Column({ type: 'boolean' })
  isAdmin: boolean;

  @Column({ type: 'boolean' })
  isInstructor: boolean;
}
