import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '@src/common/guards/jwt-auth.guard';
import { MessageService } from '../services/message.service';
import { CreateMessageDto } from '../dto/create-message.dto';

@ApiTags('Messages')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('messages')
export class MessageController {
  constructor(private readonly messageService: MessageService) {}

  @Post()
  @ApiOperation({ summary: 'Send a new message' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  create(@Body() createMessageDto: CreateMessageDto, @Request() req) {
    return this.messageService.create(createMessageDto, req.user.id);
  }

  @Get('chatroom/:chatroomId')
  @ApiOperation({ summary: 'Get messages for a specific chatroom' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Messages per page (default: 50)' })
  @ApiResponse({ status: 200, description: 'List of messages' })
  @ApiResponse({ status: 404, description: 'Chatroom not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  findByChatroom(
    @Param('chatroomId') chatroomId: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '50',
    @Request() req,
  ) {
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 50;
    return this.messageService.findByChatroom(chatroomId, req.user.id, pageNum, limitNum);
  }

  @Get('chatroom/:chatroomId/recent')
  @ApiOperation({ summary: 'Get recent messages for a specific chatroom' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of recent messages (default: 10)' })
  @ApiResponse({ status: 200, description: 'List of recent messages' })
  @ApiResponse({ status: 404, description: 'Chatroom not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  getRecentMessages(
    @Param('chatroomId') chatroomId: string,
    @Query('limit') limit: string = '10',
    @Request() req,
  ) {
    const limitNum = parseInt(limit, 10) || 10;
    return this.messageService.getRecentMessages(chatroomId, req.user.id, limitNum);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific message' })
  @ApiResponse({ status: 200, description: 'Message details' })
  @ApiResponse({ status: 404, description: 'Message not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.messageService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Edit a message' })
  @ApiResponse({ status: 200, description: 'Message updated successfully' })
  @ApiResponse({ status: 404, description: 'Message not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  update(
    @Param('id') id: string,
    @Body('content') content: string,
    @Request() req,
  ) {
    return this.messageService.update(id, content, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a message' })
  @ApiResponse({ status: 200, description: 'Message deleted successfully' })
  @ApiResponse({ status: 404, description: 'Message not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  remove(@Param('id') id: string, @Request() req) {
    return this.messageService.remove(id, req.user.id);
  }
}
