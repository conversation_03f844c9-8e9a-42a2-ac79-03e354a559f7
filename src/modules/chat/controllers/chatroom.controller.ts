import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@src/common/guards/jwt-auth.guard';
import { ChatroomService } from '../services/chatroom.service';
import { CreateChatroomDto } from '../dto/create-chatroom.dto';
import { UpdateChatroomDto } from '../dto/update-chatroom.dto';
import { JoinChatroomDto } from '../dto/join-chatroom.dto';

@ApiTags('Chatrooms')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('chatrooms')
export class ChatroomController {
  constructor(private readonly chatroomService: ChatroomService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new chatroom' })
  @ApiResponse({ status: 201, description: 'Chatroom created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  create(@Body() createChatroomDto: CreateChatroomDto, @Request() req) {
    return this.chatroomService.create(createChatroomDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all chatrooms for the current user' })
  @ApiResponse({ status: 200, description: 'List of chatrooms' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  findAll(@Request() req) {
    return this.chatroomService.findAll(req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific chatroom' })
  @ApiResponse({ status: 200, description: 'Chatroom details' })
  @ApiResponse({ status: 404, description: 'Chatroom not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.chatroomService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a chatroom' })
  @ApiResponse({ status: 200, description: 'Chatroom updated successfully' })
  @ApiResponse({ status: 404, description: 'Chatroom not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  update(
    @Param('id') id: string,
    @Body() updateChatroomDto: UpdateChatroomDto,
    @Request() req,
  ) {
    return this.chatroomService.update(id, updateChatroomDto, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a chatroom' })
  @ApiResponse({ status: 200, description: 'Chatroom deleted successfully' })
  @ApiResponse({ status: 404, description: 'Chatroom not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  remove(@Param('id') id: string, @Request() req) {
    return this.chatroomService.remove(id, req.user.id);
  }

  @Post('join')
  @ApiOperation({ summary: 'Join a chatroom' })
  @ApiResponse({ status: 200, description: 'Successfully joined chatroom' })
  @ApiResponse({ status: 404, description: 'Chatroom not found' })
  join(@Body() joinChatroomDto: JoinChatroomDto, @Request() req) {
    return this.chatroomService.joinChatroom(joinChatroomDto.chatroomId, req.user.id);
  }

  @Post(':id/leave')
  @ApiOperation({ summary: 'Leave a chatroom' })
  @ApiResponse({ status: 200, description: 'Successfully left chatroom' })
  @ApiResponse({ status: 404, description: 'Chatroom not found' })
  @ApiResponse({ status: 403, description: 'Access denied' })
  leave(@Param('id') id: string, @Request() req) {
    return this.chatroomService.leaveChatroom(id, req.user.id);
  }
}
