import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ChatroomDocument = Chatroom & Document;

@Schema({
  timestamps: true,
  collection: 'chatrooms',
})
export class Chatroom {
  @Prop({ type: Types.ObjectId, auto: true })
  _id: Types.ObjectId;

  @Prop({ required: true, trim: true, maxlength: 100 })
  name: string;

  @Prop({ trim: true, maxlength: 500 })
  description?: string;

  @Prop({
    type: [{ type: Types.ObjectId, ref: 'User' }],
    default: [],
    index: true,
  })
  participants: Types.ObjectId[];

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy: Types.ObjectId;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;

  // Virtual field for ID as string
  get id(): string {
    return this._id.toHexString();
  }
}

export const ChatroomSchema = SchemaFactory.createForClass(Chatroom);

// Create indexes
ChatroomSchema.index({ participants: 1 });
ChatroomSchema.index({ createdBy: 1 });
ChatroomSchema.index({ createdAt: -1 });
ChatroomSchema.index({ name: 'text', description: 'text' });

// Transform output to include id field and remove _id and __v
ChatroomSchema.set('toJSON', {
  transform: function (doc, ret) {
    (ret as any).id = ret._id.toString();
    delete (ret as any)._id;
    delete (ret as any).__v;
    return ret;
  },
});

ChatroomSchema.set('toObject', {
  transform: function (doc, ret) {
    (ret as any).id = ret._id.toString();
    delete (ret as any)._id;
    delete (ret as any).__v;
    return ret;
  },
});
