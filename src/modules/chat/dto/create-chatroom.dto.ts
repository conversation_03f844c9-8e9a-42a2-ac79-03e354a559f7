import { Is<PERSON><PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateChatroomDto {
  @ApiProperty({
    description: 'Name of the chatroom',
    example: 'General Discussion',
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({
    description: 'Description of the chatroom',
    example: 'A place for general discussions',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({
    description: 'Array of user IDs to add as participants',
    example: ['60d5ecb74b24c72b8c8b4567', '60d5ecb74b24c72b8c8b4568'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  participants?: string[];
}
