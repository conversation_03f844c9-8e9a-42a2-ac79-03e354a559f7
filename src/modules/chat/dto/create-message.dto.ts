import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { MessageType } from '../schemas/message.schema';

export class CreateMessageDto {
  @ApiProperty({
    description: 'ID of the chatroom',
    example: '60d5ecb74b24c72b8c8b4567',
  })
  @IsString()
  chatroomId: string;

  @ApiProperty({
    description: 'Content of the message',
    example: 'Hello everyone!',
    minLength: 1,
    maxLength: 2000,
  })
  @IsString()
  @MinLength(1)
  @MaxLength(2000)
  content: string;

  @ApiPropertyOptional({
    description: 'Type of the message',
    enum: MessageType,
    example: MessageType.TEXT,
  })
  @IsOptional()
  @IsEnum(MessageType)
  messageType?: MessageType;

  @ApiPropertyOptional({
    description: 'Additional metadata for the message',
    example: {
      fileName: 'document.pdf',
      fileSize: 1024,
      mimeType: 'application/pdf'
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: {
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
    imageUrl?: string;
    thumbnailUrl?: string;
  };
}
