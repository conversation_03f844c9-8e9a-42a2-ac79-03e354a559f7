import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatroomService } from './services/chatroom.service';
import { MessageService } from './services/message.service';
import { ChatroomController } from './controllers/chatroom.controller';
import { MessageController } from './controllers/message.controller';
import { Chatroom, ChatroomSchema } from './schemas/chatroom.schema';
import { Message, MessageSchema } from './schemas/message.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Chatroom.name, schema: ChatroomSchema },
      { name: Message.name, schema: MessageSchema },
    ]),
  ],
  controllers: [ChatroomController, MessageController],
  providers: [ChatroomService, MessageService],
  exports: [ChatroomService, MessageService],
})
export class ChatModule {}
