# Chat Module - MongoDB Integration

This module provides real-time chat functionality using MongoDB for data persistence alongside the existing PostgreSQL database.

## Overview

The chat module includes:
- **Chatrooms**: Group conversations with multiple participants
- **Messages**: Text, image, file, and system messages
- **Real-time communication**: WebSocket-based messaging via Socket.IO
- **MongoDB storage**: Optimized for chat data with proper indexing

## Architecture

### Database Design
- **PostgreSQL**: Continues to handle user management, authentication, and other application data
- **MongoDB**: Handles chat-specific data (chatrooms and messages) for better performance with real-time messaging

### Collections

#### Chatrooms Collection
```javascript
{
  _id: ObjectId,
  name: String,
  description: String,
  participants: [ObjectId], // References to User IDs
  createdBy: ObjectId,      // Reference to User ID
  createdAt: Date,
  updatedAt: Date
}
```

#### Messages Collection
```javascript
{
  _id: ObjectId,
  chatroomId: ObjectId,     // Reference to Chatroom
  senderId: ObjectId,       // Reference to User ID
  content: String,
  messageType: String,      // 'text', 'image', 'file', 'system'
  metadata: Object,         // File info, image URLs, etc.
  isEdited: Boolean,
  editedAt: Date,
  createdAt: Date,
  updatedAt: Date
}
```

## Setup Instructions

### 1. Environment Configuration

Add MongoDB configuration to your `.env` file:

```bash
# Option 1: Connection string
MONGODB_URI=mongodb://localhost:27017/sit_chat

# Option 2: Individual parameters
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=sit_chat
MONGODB_USERNAME=
MONGODB_PASSWORD=
MONGODB_AUTH_SOURCE=admin
MONGODB_MAX_CONNECTIONS=100
MONGODB_SSL_ENABLED=false
```

### 2. Initialize MongoDB Collections

Run the initialization script to create collections and indexes:

```bash
npm run mongodb:init
```

To drop collections (for development):

```bash
npm run mongodb:drop
```

### 3. Start the Application

The application will now connect to both PostgreSQL and MongoDB:

```bash
npm run start:dev
```

## API Endpoints

### Chatrooms
- `POST /chatrooms` - Create a new chatroom
- `GET /chatrooms` - Get user's chatrooms
- `GET /chatrooms/:id` - Get specific chatroom
- `PATCH /chatrooms/:id` - Update chatroom
- `DELETE /chatrooms/:id` - Delete chatroom
- `POST /chatrooms/join` - Join a chatroom
- `POST /chatrooms/:id/leave` - Leave a chatroom

### Messages
- `POST /messages` - Send a message
- `GET /messages/chatroom/:chatroomId` - Get messages for a chatroom
- `GET /messages/chatroom/:chatroomId/recent` - Get recent messages
- `GET /messages/:id` - Get specific message
- `PATCH /messages/:id` - Edit a message
- `DELETE /messages/:id` - Delete a message

## WebSocket Events

### Client to Server
- `joinRoom` - Join a chatroom
- `leaveRoom` - Leave a chatroom
- `sendMessage` - Send a message
- `typing` - Typing indicator

### Server to Client
- `joinedRoom` - Confirmation of joining
- `leftRoom` - Confirmation of leaving
- `userJoined` - Another user joined
- `userLeft` - Another user left
- `newMessage` - New message received
- `userTyping` - Typing indicator
- `recentMessages` - Recent messages on join
- `error` - Error messages

## Usage Examples

### Joining a Chatroom (WebSocket)
```javascript
socket.emit('joinRoom', { chatroomId: '60d5ecb74b24c72b8c8b4567' });

socket.on('joinedRoom', (data) => {
  console.log('Joined chatroom:', data.chatroom);
});

socket.on('recentMessages', (data) => {
  console.log('Recent messages:', data.messages);
});
```

### Sending a Message (WebSocket)
```javascript
socket.emit('sendMessage', {
  chatroomId: '60d5ecb74b24c72b8c8b4567',
  content: 'Hello everyone!',
  messageType: 'text'
});

socket.on('newMessage', (data) => {
  console.log('New message:', data.message);
});
```

### Creating a Chatroom (REST API)
```javascript
const response = await fetch('/chatrooms', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    name: 'General Discussion',
    description: 'A place for general discussions',
    participants: ['60d5ecb74b24c72b8c8b4568']
  })
});
```

## Performance Considerations

### Indexes
The module automatically creates optimized indexes for:
- Chatroom participants lookup
- Message retrieval by chatroom and timestamp
- Text search on chatroom names and message content
- User-specific queries

### Pagination
Messages are paginated with configurable limits to handle large chat histories efficiently.

### Real-time Updates
Socket.IO with Redis adapter ensures scalability across multiple server instances.

## Security

- JWT authentication required for all operations
- Users can only access chatrooms they're participants in
- Message editing/deletion restricted to message authors
- Chatroom management restricted to creators
