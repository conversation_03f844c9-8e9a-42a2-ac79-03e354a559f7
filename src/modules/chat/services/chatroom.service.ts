import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Chatroom, ChatroomDocument } from '../schemas/chatroom.schema';
import { CreateChatroomDto } from '../dto/create-chatroom.dto';
import { UpdateChatroomDto } from '../dto/update-chatroom.dto';

@Injectable()
export class ChatroomService {
  constructor(
    @InjectModel(Chatroom.name) private chatroomModel: Model<ChatroomDocument>,
  ) {}

  async create(
    createChatroomDto: CreateChatroomDto,
    userId: string,
  ): Promise<ChatroomDocument> {
    const participants = createChatroomDto.participants || [];

    // Add creator to participants if not already included
    if (!participants.includes(userId)) {
      participants.push(userId);
    }

    const chatroom = new this.chatroomModel({
      ...createChatroomDto,
      participants: participants.map((id) => new Types.ObjectId(id)),
      createdBy: new Types.ObjectId(userId),
    });

    return chatroom.save();
  }

  async findAll(userId: string): Promise<ChatroomDocument[]> {
    return this.chatroomModel
      .find({ participants: new Types.ObjectId(userId) })
      .populate('participants', 'id email firstName lastName')
      .populate('createdBy', 'id email firstName lastName')
      .sort({ updatedAt: -1 })
      .exec();
  }

  async findOne(id: string, userId: string): Promise<ChatroomDocument> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid chatroom ID');
    }

    const chatroom = await this.chatroomModel
      .findById(id)
      .populate('participants', 'id email firstName lastName')
      .populate('createdBy', 'id email firstName lastName')
      .exec();

    if (!chatroom) {
      throw new NotFoundException('Chatroom not found');
    }

    // Check if user is a participant
    const isParticipant = chatroom.participants.some(
      (participant) => participant.toString() === userId,
    );

    if (!isParticipant) {
      throw new ForbiddenException(
        'You are not a participant in this chatroom',
      );
    }

    return chatroom;
  }

  async update(
    id: string,
    updateChatroomDto: UpdateChatroomDto,
    userId: string,
  ): Promise<ChatroomDocument> {
    const chatroom = await this.findOne(id, userId);

    // Only creator can update chatroom details
    if (chatroom.createdBy.toString() !== userId) {
      throw new ForbiddenException('Only the creator can update this chatroom');
    }

    Object.assign(chatroom, updateChatroomDto);
    chatroom.updatedAt = new Date();

    return chatroom.save();
  }

  async remove(id: string, userId: string): Promise<void> {
    const chatroom = await this.findOne(id, userId);

    // Only creator can delete chatroom
    if (chatroom.createdBy.toString() !== userId) {
      throw new ForbiddenException('Only the creator can delete this chatroom');
    }

    await this.chatroomModel.findByIdAndDelete(id);
  }

  async joinChatroom(
    chatroomId: string,
    userId: string,
  ): Promise<ChatroomDocument> {
    if (!Types.ObjectId.isValid(chatroomId)) {
      throw new NotFoundException('Invalid chatroom ID');
    }

    const chatroom = await this.chatroomModel.findById(chatroomId);

    if (!chatroom) {
      throw new NotFoundException('Chatroom not found');
    }

    const userObjectId = new Types.ObjectId(userId);

    // Check if user is already a participant
    const isAlreadyParticipant = chatroom.participants.some((participant) =>
      participant.equals(userObjectId),
    );

    if (!isAlreadyParticipant) {
      chatroom.participants.push(userObjectId);
      chatroom.updatedAt = new Date();
      await chatroom.save();
    }

    const populatedChatroom = await this.chatroomModel
      .findById(chatroomId)
      .populate('participants', 'id email firstName lastName')
      .populate('createdBy', 'id email firstName lastName')
      .exec();

    if (!populatedChatroom) {
      throw new NotFoundException('Chatroom not found');
    }

    return populatedChatroom;
  }

  async leaveChatroom(chatroomId: string, userId: string): Promise<void> {
    const chatroom = await this.findOne(chatroomId, userId);

    const userObjectId = new Types.ObjectId(userId);
    chatroom.participants = chatroom.participants.filter(
      (participant) => !participant.equals(userObjectId),
    );

    chatroom.updatedAt = new Date();
    await chatroom.save();
  }
}
