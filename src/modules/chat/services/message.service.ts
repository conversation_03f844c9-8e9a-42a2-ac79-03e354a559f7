import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Message, MessageDocument } from '../schemas/message.schema';
import { CreateMessageDto } from '../dto/create-message.dto';
import { ChatroomService } from './chatroom.service';

@Injectable()
export class MessageService {
  constructor(
    @InjectModel(Message.name) private messageModel: Model<MessageDocument>,
    private chatroomService: ChatroomService,
  ) {}

  async create(
    createMessageDto: CreateMessageDto,
    userId: string,
  ): Promise<MessageDocument> {
    // Verify user is a participant in the chatroom
    await this.chatroomService.findOne(createMessageDto.chatroomId, userId);

    const message = new this.messageModel({
      ...createMessageDto,
      chatroomId: new Types.ObjectId(createMessageDto.chatroomId),
      senderId: new Types.ObjectId(userId),
    });

    const savedMessage = await message.save();

    // Populate sender information
    const populatedMessage = await this.messageModel
      .findById(savedMessage._id)
      .populate('senderId', 'id email firstName lastName')
      .exec();

    if (!populatedMessage) {
      throw new NotFoundException('Message not found after creation');
    }

    return populatedMessage;
  }

  async findByChatroom(
    chatroomId: string,
    userId: string,
    page: number = 1,
    limit: number = 50,
  ): Promise<{ messages: MessageDocument[]; total: number; hasMore: boolean }> {
    // Verify user is a participant in the chatroom
    await this.chatroomService.findOne(chatroomId, userId);

    if (!Types.ObjectId.isValid(chatroomId)) {
      throw new NotFoundException('Invalid chatroom ID');
    }

    const skip = (page - 1) * limit;
    const chatroomObjectId = new Types.ObjectId(chatroomId);

    const [messages, total] = await Promise.all([
      this.messageModel
        .find({ chatroomId: chatroomObjectId })
        .populate('senderId', 'id email firstName lastName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.messageModel.countDocuments({ chatroomId: chatroomObjectId }),
    ]);

    const hasMore = skip + messages.length < total;

    return {
      messages: messages.reverse(), // Reverse to show oldest first
      total,
      hasMore,
    };
  }

  async findOne(id: string, userId: string): Promise<MessageDocument> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid message ID');
    }

    const message = await this.messageModel
      .findById(id)
      .populate('senderId', 'id email firstName lastName')
      .exec();

    if (!message) {
      throw new NotFoundException('Message not found');
    }

    // Verify user is a participant in the chatroom
    await this.chatroomService.findOne(message.chatroomId.toString(), userId);

    return message;
  }

  async update(
    id: string,
    content: string,
    userId: string,
  ): Promise<MessageDocument> {
    const message = await this.findOne(id, userId);

    // Only sender can edit their message
    if (message.senderId.toString() !== userId) {
      throw new ForbiddenException('You can only edit your own messages');
    }

    message.content = content;
    message.isEdited = true;
    message.editedAt = new Date();
    message.updatedAt = new Date();

    return message.save();
  }

  async remove(id: string, userId: string): Promise<void> {
    const message = await this.findOne(id, userId);

    // Only sender can delete their message
    if (message.senderId.toString() !== userId) {
      throw new ForbiddenException('You can only delete your own messages');
    }

    await this.messageModel.findByIdAndDelete(id);
  }

  async getRecentMessages(
    chatroomId: string,
    userId: string,
    limit: number = 10,
  ): Promise<MessageDocument[]> {
    // Verify user is a participant in the chatroom
    await this.chatroomService.findOne(chatroomId, userId);

    if (!Types.ObjectId.isValid(chatroomId)) {
      throw new NotFoundException('Invalid chatroom ID');
    }

    return this.messageModel
      .find({ chatroomId: new Types.ObjectId(chatroomId) })
      .populate('senderId', 'id email firstName lastName')
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();
  }
}
