import { Injectable } from '@nestjs/common';
import { MailerService } from '../mailer/mailer.service';
import { AllConfigType } from '../../common/configs/config.type';
import { ConfigService } from '@nestjs/config';
import { I18nService } from 'nestjs-i18n';

import * as path from 'path';
import { promises as fs } from 'fs';
import { CustomLogger } from '@src/common/logging';

import * as sharp from 'sharp';
import * as QRCode from 'qrcode';

import { DataSource } from 'typeorm';
import { findIndex } from 'lodash';
import * as dayjs from 'dayjs';

interface Lounge {
  id: number;
  name: string;
  code: string;
  location: string;
}

@Injectable()
export class MailService {
  private loggerMeta: any;
  private defaultReplyTo: string;

  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService<AllConfigType>,

    private readonly i18n: I18nService,
    private readonly logger: CustomLogger,
    private readonly dataSource: DataSource,
  ) {
    this.loggerMeta = { context: MailService.name };
    this.defaultReplyTo =
      this.configService.get('mail.replyTo', { infer: true }) || '';
  }
  async sendVerificationEmail(lang = 'vi', email: string, token: string) {
    const host = this.configService.get('app.frontendDomain', { infer: true });
    const url = `${host}/verify-email?token=${token}`;
    const workingDir =
      process.env.NODE_ENV == 'development'
        ? `${process.cwd()}/src/modules/mail`
        : __dirname;
    await this.mailerService.sendMail({
      to: email,
      subject: this.i18n.t('email.subject.verify', { lang }),
      text: this.i18n.t('email.body.verify', { lang, args: { url } }),
      templatePath: path.join(
        workingDir,
        'mail-templates',
        'i18n',
        lang,
        'active-account.hbs',
      ),
      context: {
        title: this.i18n.t('email.subject.verify', { lang }),
        url: url,
        actionTitle: this.i18n.t('email.subject.verify', { lang }),
      },
      replyTo: this.defaultReplyTo,
    });
  }

  async sendPasswordResetEmail(lang = 'vi', email: string, token: string) {
    const host = this.configService.get('app.frontendDomain', { infer: true });
    const url = `${host}/verify-password?token=${token}`;
    const workingDir =
      process.env.NODE_ENV == 'development'
        ? `${process.cwd()}/src/modules/mail`
        : __dirname;
    await this.mailerService.sendMail({
      to: email,
      subject: this.i18n.t('email.subject.reset_password', { lang }),
      text: this.i18n.t('email.body.reset_password', { lang, args: { url } }),
      templatePath: path.join(
        workingDir,
        'mail-templates',
        'i18n',
        lang,
        'reset-password.hbs',
      ),
      context: {
        title: this.i18n.t('email.subject.reset_password', { lang }),
        url: url,
        actionTitle: this.i18n.t('email.subject.reset_password', { lang }),
      },
      replyTo: this.defaultReplyTo,
    });
  }
}
