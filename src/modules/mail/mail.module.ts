import { MailerModule } from '@nestjs-modules/mailer';
import { Module } from '@nestjs/common';
import { MailService } from './mail.service';
import { MailerService } from '../mailer/mailer.service';
import { CustomLogger } from '@src/common/logging';
@Module({
  // imports: [
  //   MailerModule.forRoot({
  //     transport: {
  //       host: 'smtp.example.com',
  //       port: 587,
  //       secure: false,
  //       auth: {
  //         user: '<EMAIL>',
  //         pass: 'your-password',
  //       },
  //     },
  //     defaults: {
  //       from: '"No Reply" <<EMAIL>>',
  //     },
  //   }),
  // ],
  providers: [MailService, MailerService, CustomLogger],
  exports: [MailService, MailerService, CustomLogger],
})
export class MailModule {}
