import { Injectable } from '@nestjs/common';
import * as fs from 'node:fs/promises';
import { ConfigService } from '@nestjs/config';
// import nodemailer from 'nodemailer';
import Handlebars from 'handlebars';
import { AllConfigType } from '../../common/configs/config.type';
import * as nodemailer from 'nodemailer';
import { TransportOptions } from 'nodemailer';

@Injectable()
export class MailerService {
  private readonly transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService<AllConfigType>) {
    this.transporter = nodemailer.createTransport({
      host: configService.get('mail.host', { infer: true }),
      port: configService.get('mail.port', { infer: true }),
      secure: configService.get('mail.secure', { infer: true }),
      auth: {
        user: configService.get('mail.user', { infer: true }),
        pass: configService.get('mail.password', { infer: true }),
      },
      tls: {
        rejectUnauthorized: false,
        ignoreTLS: configService.get('mail.ignoreTLS', { infer: true }),
        requireTLS: configService.get('mail.requireTLS', { infer: true }),
      },
    } as TransportOptions);
  }
  async sendMail({
    templatePath,
    context,
    ...mailOptions
  }: nodemailer.SendMailOptions & {
    templatePath: string;
    context: Record<string, unknown>;
  }): Promise<void> {
    let html: string | undefined;
    if (templatePath) {
      const template = await fs.readFile(templatePath, 'utf-8');
      html = Handlebars.compile(template, {
        strict: true,
      })(context);
    }

    const mailFrom = mailOptions.from
      ? mailOptions.from
      : `"${this.configService.get('mail.defaultName', { infer: true })}" <${this.configService.get('mail.defaultEmail', { infer: true })}>`;

    await this.transporter.sendMail({
      ...mailOptions,
      from: mailFrom,
      html: mailOptions.html ? mailOptions.html : html,
    });
  }
}
